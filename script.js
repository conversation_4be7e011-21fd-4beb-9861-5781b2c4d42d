// Global variables to store reCAPTCHA widget IDs
let recaptchaDarkId = null;
let recaptchaLightId = null;

// Function to get current theme
function getCurrentTheme() {
    const htmlElement = document.documentElement;
    return htmlElement.classList.contains('dark') ? 'dark' : 'light';
}

// Function to initialize both reCAPTCHA widgets
function initializeRecaptcha() {
    const recaptchaContainer = document.getElementById('recaptcha-widget');
    if (recaptchaContainer && typeof grecaptcha !== 'undefined' && grecaptcha.render) {
        // Create containers for both themes
        recaptchaContainer.innerHTML = `
            <div id="recaptcha-dark" style="display: block;"></div>
            <div id="recaptcha-light" style="display: none;"></div>
        `;

        // Render both widgets
        recaptchaDarkId = grecaptcha.render('recaptcha-dark', {
            'sitekey': '6LdLmHMrAAAAAOjVgSLRiO4bmQx2T07gWNT01nT0',
            'theme': 'dark'
        });

        recaptchaLightId = grecaptcha.render('recaptcha-light', {
            'sitekey': '6LdLmHMrAAAAAOjVgSLRiO4bmQx2T07gWNT01nT0',
            'theme': 'light'
        });

        // Show the correct widget based on current theme
        switchRecaptchaTheme();
    }
}

// Function to switch between reCAPTCHA themes
function switchRecaptchaTheme() {
    const darkWidget = document.getElementById('recaptcha-dark');
    const lightWidget = document.getElementById('recaptcha-light');
    const currentTheme = getCurrentTheme();

    if (darkWidget && lightWidget) {
        if (currentTheme === 'dark') {
            darkWidget.style.display = 'block';
            lightWidget.style.display = 'none';
        } else {
            darkWidget.style.display = 'none';
            lightWidget.style.display = 'block';
        }
    }
}

// Function called when reCAPTCHA API loads
window.onRecaptchaLoad = function() {
    initializeRecaptcha();
};

document.addEventListener('DOMContentLoaded', () => {
    // Video wallpaper with placeholder functionality
    const globalVideo = document.getElementById('global-video');

    if (globalVideo) {
        // Initially hide the video and show placeholder
        globalVideo.style.opacity = '0';
        globalVideo.style.transition = 'opacity 0.5s ease-in-out';

        // Set placeholder background on body
        document.body.style.backgroundImage = 'url(/images/wallpaper.png)';
        document.body.classList.add('video-loading');

        // Function to show video and hide placeholder
        const showVideo = () => {
            globalVideo.style.opacity = '1';
            // Remove placeholder background after video is visible
            setTimeout(() => {
                document.body.style.backgroundImage = 'none';
                document.body.classList.remove('video-loading');
            }, 500); // Wait for fade-in transition to complete
        };

        // Check if video can play through without buffering
        globalVideo.addEventListener('canplaythrough', showVideo);

        // Fallback: show video after it starts playing (in case canplaythrough doesn't fire)
        globalVideo.addEventListener('playing', () => {
            if (globalVideo.style.opacity === '0') {
                showVideo();
            }
        });

        // Error handling: if video fails to load, keep the placeholder
        globalVideo.addEventListener('error', () => {
            console.warn('Video wallpaper failed to load, keeping placeholder image');
        });
    }

    // Theme toggle functionality
    const themeToggle = document.getElementById('theme-toggle');
    const htmlElement = document.documentElement;
    
    // Check for saved theme preference or use default dark theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        htmlElement.className = savedTheme;
    } else {
        // Default to dark theme if no preference is saved
        htmlElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
    }
    
    themeToggle.addEventListener('click', () => {
        if (htmlElement.classList.contains('dark')) {
            htmlElement.classList.remove('dark');
            localStorage.setItem('theme', '');
        } else {
            htmlElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
        }

        // Switch reCAPTCHA theme
        switchRecaptchaTheme();
    });
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            window.scrollTo({
                top: targetElement.offsetTop - 80,
                behavior: 'smooth'
            });
        });
    });
    
    // Form submission handling
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // Check if reCAPTCHA is completed (check the currently visible widget)
            const currentTheme = getCurrentTheme();
            const currentWidgetId = currentTheme === 'dark' ? recaptchaDarkId : recaptchaLightId;
            const recaptchaResponse = currentWidgetId !== null ? grecaptcha.getResponse(currentWidgetId) : '';
            if (!recaptchaResponse) {
                alert('Please complete the reCAPTCHA verification.');
                return;
            }

            // In a real implementation, you would send the form data to a server
            // For this example, we'll just show a success message
            const formData = new FormData(contactForm);
            let formValues = {};

            for (let [key, value] of formData.entries()) {
                formValues[key] = value;
            }

            // Add reCAPTCHA response to form data
            formValues['g-recaptcha-response'] = recaptchaResponse;

            console.log('Form submitted:', formValues);

            // Reset form and show success message
            contactForm.reset();
            if (recaptchaWidgetId !== null) {
                grecaptcha.reset(recaptchaWidgetId); // Reset reCAPTCHA
            }
            alert('Thank you for your message! We will get back to you soon.');
        });
    }


    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('nav ul');

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a nav link
    document.querySelectorAll('nav ul li a').forEach(link => {
        link.addEventListener('click', () => {
            if (window.innerWidth <= 768) {
                navMenu.classList.remove('active');
            }
        });
    });
});

