// Global variables to store reCAPTCHA widget IDs
let recaptchaDarkId = null;
let recaptchaLightId = null;

// Function to get current theme
function getCurrentTheme() {
    const htmlElement = document.documentElement;
    return htmlElement.classList.contains('dark') ? 'dark' : 'light';
}

// Function to initialize both reCAPTCHA widgets
function initializeRecaptcha() {
    const recaptchaContainer = document.getElementById('recaptcha-widget');
    if (recaptchaContainer && typeof grecaptcha !== 'undefined' && grecaptcha.render) {
        // Create containers for both themes
        recaptchaContainer.innerHTML = `
            <div id="recaptcha-dark" style="display: block;"></div>
            <div id="recaptcha-light" style="display: none;"></div>
        `;

        // Render both widgets
        recaptchaDarkId = grecaptcha.render('recaptcha-dark', {
            'sitekey': '6LdLmHMrAAAAAOjVgSLRiO4bmQx2T07gWNT01nT0',
            'theme': 'dark'
        });

        recaptchaLightId = grecaptcha.render('recaptcha-light', {
            'sitekey': '6LdLmHMrAAAAAOjVgSLRiO4bmQx2T07gWNT01nT0',
            'theme': 'light'
        });

        // Show the correct widget based on current theme
        switchRecaptchaTheme();
    }
}

// Function to switch between reCAPTCHA themes
function switchRecaptchaTheme() {
    const darkWidget = document.getElementById('recaptcha-dark');
    const lightWidget = document.getElementById('recaptcha-light');
    const currentTheme = getCurrentTheme();

    if (darkWidget && lightWidget) {
        if (currentTheme === 'dark') {
            darkWidget.style.display = 'block';
            lightWidget.style.display = 'none';
        } else {
            darkWidget.style.display = 'none';
            lightWidget.style.display = 'block';
        }
    }
}

// Function called when reCAPTCHA API loads
window.onRecaptchaLoad = function() {
    initializeRecaptcha();
};

document.addEventListener('DOMContentLoaded', () => {
    // Initialize particles.js for hero section
    console.log('Particles.js available:', typeof particlesJS !== 'undefined');
    if (typeof particlesJS !== 'undefined') {
        console.log('Initializing particles...');
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 80,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#007db6"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    }
                },
                "opacity": {
                    "value": 0.5,
                    "random": false,
                    "anim": {
                        "enable": false,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": false,
                        "speed": 40,
                        "size_min": 0.1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#007db6",
                    "opacity": 0.4,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 6,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "repulse"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 400,
                        "line_linked": {
                            "opacity": 1
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        }, function() {
            console.log('Particles.js loaded successfully!');
        });
    } else {
        console.log('Particles.js not found!');
    }

    // Theme toggle functionality
    const themeToggle = document.getElementById('theme-toggle');
    const htmlElement = document.documentElement;
    
    // Check for saved theme preference or use default dark theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        htmlElement.className = savedTheme;
    } else {
        // Default to dark theme if no preference is saved
        htmlElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
    }
    
    themeToggle.addEventListener('click', () => {
        if (htmlElement.classList.contains('dark')) {
            htmlElement.classList.remove('dark');
            localStorage.setItem('theme', '');
        } else {
            htmlElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
        }

        // Switch reCAPTCHA theme
        switchRecaptchaTheme();
    });
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            window.scrollTo({
                top: targetElement.offsetTop - 80,
                behavior: 'smooth'
            });
        });
    });
    
    // Form submission handling
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // Check if reCAPTCHA is completed (check the currently visible widget)
            const currentTheme = getCurrentTheme();
            const currentWidgetId = currentTheme === 'dark' ? recaptchaDarkId : recaptchaLightId;
            const recaptchaResponse = currentWidgetId !== null ? grecaptcha.getResponse(currentWidgetId) : '';
            if (!recaptchaResponse) {
                alert('Please complete the reCAPTCHA verification.');
                return;
            }

            // In a real implementation, you would send the form data to a server
            // For this example, we'll just show a success message
            const formData = new FormData(contactForm);
            let formValues = {};

            for (let [key, value] of formData.entries()) {
                formValues[key] = value;
            }

            // Add reCAPTCHA response to form data
            formValues['g-recaptcha-response'] = recaptchaResponse;

            console.log('Form submitted:', formValues);

            // Reset form and show success message
            contactForm.reset();
            // Reset both reCAPTCHA widgets
            if (recaptchaDarkId !== null) {
                grecaptcha.reset(recaptchaDarkId);
            }
            if (recaptchaLightId !== null) {
                grecaptcha.reset(recaptchaLightId);
            }
            alert('Thank you for your message! We will get back to you soon.');
        });
    }


    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('nav ul');

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a nav link
    document.querySelectorAll('nav ul li a').forEach(link => {
        link.addEventListener('click', () => {
            if (window.innerWidth <= 768) {
                navMenu.classList.remove('active');
            }
        });
    });
});

