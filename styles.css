/* Import Space Grotesk font */
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@700&display=swap');

/* Apply to section headings */
h1, h2, h3 {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
}

/* Apply to buttons */
.btn, button, .read-more-btn {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
}

:root {
    --bg-primary: #f8f9fa;
    --bg-secondary: #ffffff;
    --text-primary: #212529;
    --text-secondary: #495057;
    --accent: RGB(0,125,182);
    --accent-hover: #005d85;
    --border:RGB(0,125,182);
    --card-bg: #ffffff;
    --shadow: rgba(0, 0, 0, 0.1);
    --header-bg: #101820; /* RGB 16,24,32 - consistent header background */
    --divider-color: #c0c0c0; /* Light grey for section dividers */
}

.dark {
    --bg-primary: #121212;
    --bg-secondary: #101820; /* RGB 16,24,32 */
    --text-primary: #f8f9fa;
    --text-secondary: #adb5bd;
    --accent: RGB(0,125,182);
    --accent-hover:  #005d85;
    --border: RGB(0,125,182);
    --card-bg: #2d2d2d;
    --shadow: rgba(0, 0, 0, 0.3);
    --header-bg: #101820; /* RGB 16,24,32 - consistent header background */
    --divider-color: #808080; /* Slightly darker grey for dark theme dividers */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color 0.3s, color 0.3s;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background-color: var(--header-bg);
    box-shadow: 0 2px 10px var(--shadow);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 20px;
}

/* Logo positioning */
.logo {
    flex: 0 0 auto;
    padding-left: 20px;
}

.logo img {
    width: auto;
    height: 100%;
    vertical-align: middle;
    margin-top: -12px;
    margin-bottom: -12px;
    transition: height 0.3s ease;
    min-height: 70px;
}

nav {
    flex: 1 1 auto;
    display: flex;
    justify-content: center;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 15px;
}

nav ul li a {
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s;
    padding: 8px 15px;
    border: 1px solid var(--border);
    border-radius: 5px;
    background-color: var(--bg-secondary);
    box-shadow: 0 2px 4px var(--shadow);
    display: inline-block;
}

nav ul li a:hover {
    color: var(--accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow);
    border-color: var(--accent);
}

nav ul li a:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px var(--shadow);
}

/* Theme toggle positioning */
#theme-toggle {
    flex: 0 0 auto;
    padding-right: 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
}

.sun-icon, .moon-icon {
    display: inline-block;
}

html.dark .sun-icon {
    display: inline-block;
}

html.dark .moon-icon {
    display: none;
}

html:not(.dark) .sun-icon {
    display: none;
}

html:not(.dark) .moon-icon {
    display: inline-block;
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #f8f9fa; /* Light color for better visibility in both modes */
    font-size: 2rem;
    cursor: pointer;
    padding: 5px;
    position: fixed;
    transform: translateX(-100%);
}

@media (max-width: 768px) {
    header .container {
        position: relative; /* Ensure the absolute positioning works correctly */
    }
    
    .mobile-menu-toggle {
        display: block;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .logo img {
        height: 40px; /* Scale down logo on mobile, but not below min-height */
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    nav ul {
        display: none; /* Hide navigation by default on mobile */
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: var(--header-bg);
        flex-direction: column;
        padding: 20px 0;
        box-shadow: 0 5px 10px var(--shadow);
    }
    
    nav ul.active {
        display: flex; /* Show navigation when active class is added */
    }
    
    nav ul li {
        margin: 10px 0;
        width: 100%;
        text-align: center;
    }
    
    nav ul li a {
        width: 80%;
        margin: 0 auto;
    }
}

@media (max-width: 480px) {
    .logo img {
        height: 40px; /* Keep logo at this size even on smaller devices */
    }
}

/* Update nav buttons to work with dark header in light mode */
html:not(.dark) nav ul li a {
    background-color: rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
}

html:not(.dark) nav ul li a:hover {
    color: var(--accent);
}

/* Light theme - make only headings and labels light grey */
html:not(.dark) section h1,
html:not(.dark) section h2,
html:not(.dark) section h3 {
    color: #e9ecef;
}

/* Light theme - exclude contact form labels from forced light color */
html:not(.dark) section label:not(.contact-card label) {
    color: #e9ecef;
}

/* About Us card styling */
.about-card {
    background-color: var(--card-bg);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow);
    position: relative;
    z-index: 2;
}

/* About Us text follows theme colors like other sections */
.about-card p {
    color: var(--text-primary);
}

/* Sections */
section {
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

/* Section borders - 1cm from browser edges */
section:not(#hero) {
    margin: 0 1cm 20px 1cm;
    border: 1px solid var(--accent);
    border-radius: 8px;
}

#hero {
    position: relative;
    height: 60vh;
    display: flex;
    align-items: center;
    text-align: center;
    color: #ffffff;
    overflow: hidden;
}

#hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

/* Section borders - exclude hero section */
section:not(#hero) {
    border: 2px solid var(--border);
    border-radius: 10px;
    margin: 20px 0;
}

section:not(#hero) .container {
    padding: 40px 20px;
}

#hero .container {
    position: relative;
    z-index: 1;
}

#hero h1 {
    font-size: 3rem;
    margin-bottom: 80px;
}

#hero p {
    font-size: 1.2rem;
    margin-bottom: 100px;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-align: center;
}

/* Global video background */
#global-video {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -2;
}

/* Placeholder background styling (applied via JavaScript) */
body.video-loading {
    background-attachment: fixed !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

/* Ensure all content appears above global video */
header {
    position: relative;
    z-index: 10;
}

section {
    position: relative;
    z-index: 1;
}

section .container {
    position: relative;
    z-index: 1;
}

/* Ensure contact form stands out from background */
.contact-card {
    position: relative;
    z-index: 2;
}

/* Services */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: var(--card-bg);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow);
    transition: transform 0.3s;
    position: relative;
    z-index: 2;
}

.service-card:hover {
    transform: translateY(-10px);
}

.service-card h3 {
    margin-bottom: 15px;
    color: var(--accent) !important;
}

/* Team */
.team-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.team-member {
    background-color: var(--card-bg);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow);
}

.member-content {
    display: flex;
    align-items: center;
    gap: 30px;
}

.member-photo {
    width: 150px;
    height: 200px;
    border-radius: 5%;
    overflow: hidden;
    flex-shrink: 0;
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    image-rendering: auto; /* Default rendering */
    filter: none; /* Remove any filters */
}

.member-info {
    flex-grow: 1;
}

.member-info h3 {
    margin-bottom: 5px;
    color: var(--accent) !important;
}

.member-title {
    font-weight: bold;
    color: var(--text-secondary);
    margin-bottom: 15px;
}

.member-bio {
    line-height: 1.6;
}

/* Responsive adjustments for team section */
@media (max-width: 768px) {
    .member-content {
        flex-direction: column;
        text-align: center;
    }

    .member-photo {
        margin: 0 auto 20px;
    }
}

/* Testimonials */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background-color: var(--card-bg);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow);
    transition: transform 0.3s;
    position: relative;
    z-index: 2;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.testimonial-text {
    font-style: italic;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
    color: var(--text-primary);
}

.testimonial-text::before {
    content: '"';
    font-size: 2rem;
    color: var(--accent);
    line-height: 1;
}

.testimonial-text::after {
    content: '"';
    font-size: 2rem;
    color: var(--accent);
    line-height: 1;
}

.testimonial-author {
    margin-top: auto;
    padding-top: 15px;
    border-top: 2px solid var(--accent);
}

.testimonial-author h4 {
    margin: 0 0 5px 0;
    color: var(--accent);
    font-size: 1.1rem;
}

.author-title {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Contact */
.contact-card {
    background-color: var(--card-bg);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow);
    position: relative;
    z-index: 2;
}

/* Contact form labels follow theme colors */
.contact-card label {
    color: var(--text-primary);
}

/* Center the contact form submit button */
.contact-card .btn {
    display: block;
    margin: 0 auto;
}

/* Center the reCAPTCHA widget */
.recaptcha-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-secondary);
}

input, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border);
    border-radius: 5px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.btn {
    display: inline-block;
    background-color: var(--accent);
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: var(--accent-hover);
}

/* Footer */
footer {
    background-color: var(--bg-secondary);
    padding: 30px 0;
    text-align: center;
}

.social-links {
    margin-top: 20px;
}

.social-links a {
    margin: 0 10px;
    color: var(--text-secondary);
    text-decoration: none;
}

/* Responsive */
@media (max-width: 768px) {
    nav ul {
        display: none;
    }
    

    #hero h1 {
        font-size: 2rem;
    }
}

@media (max-width: 1200px) {
    .logo {
        padding-left: 0; /* Remove padding on smaller screens */
    }
    
    #theme-toggle {
        padding-right: 0; /* Remove padding on smaller screens */
    }
}

@media (max-width: 768px) {
    .logo {
        padding-left: 10px; /* Less space on mobile */
    }
    
    #theme-toggle {
        padding-right: 10px; /* Less space on mobile */
    }
}





